"use client";

import { createClient } from "@supabase/supabase-js";
import { useState } from "react";

// Hardcoded Supabase credentials
const SUPABASE_URL = "https://kjuqzmooxzjymphewpkm.supabase.co";
const SUPABASE_SERVICE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtqdXF6bW9veHpqeW1waGV3cGttIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzE3OTA4MiwiZXhwIjoyMDYyNzU1MDgyfQ.DNGRyS9knu-Mu_CzYRwo1TQCyrNRyHRa9tJRpzvjSys";

// Create Supabase client with service role key for admin access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

export default function DownloadPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");

  const downloadUsersCSV = async () => {
    setIsLoading(true);
    setMessage("");

    try {
      // Fetch all users from the database
      const { data: users, error } = await supabase.from("users").select("*");

      if (error) {
        throw error;
      }

      if (!users || users.length === 0) {
        setMessage("No users found in the database.");
        setIsLoading(false);
        return;
      }

      // Convert to CSV
      const csvContent = convertToCSV(users);

      // Create and download the file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);

      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `users_${new Date().toISOString().split("T")[0]}.csv`
      );
      link.style.visibility = "hidden";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setMessage(`Successfully downloaded ${users.length} users to CSV file.`);
    } catch (error) {
      console.error("Error downloading users:", error);
      setMessage(
        `Error: ${
          error instanceof Error ? error.message : "Unknown error occurred"
        }`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const convertToCSV = (data: any[]) => {
    if (data.length === 0) return "";

    // Get all unique keys from all objects
    const allKeys = Array.from(
      new Set(data.flatMap((obj) => Object.keys(obj)))
    );

    // Create header row
    const header = allKeys.join(",");

    // Create data rows
    const rows = data.map((obj) =>
      allKeys
        .map((key) => {
          const value = obj[key];
          // Handle null/undefined values and escape commas/quotes
          if (value === null || value === undefined) return "";
          const stringValue = String(value);
          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (
            stringValue.includes(",") ||
            stringValue.includes('"') ||
            stringValue.includes("\n")
          ) {
            return `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        })
        .join(",")
    );

    return [header, ...rows].join("\n");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-8">
          <h1 className="text-3xl font-bold text-white mb-6 text-center">
            Database Download
          </h1>

          <div className="text-center space-y-6">
            <p className="text-gray-300 text-lg">
              Download all users data from the Supabase database as a CSV file.
            </p>

            <button
              onClick={downloadUsersCSV}
              disabled={isLoading}
              className={`
                px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200
                ${
                  isLoading
                    ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700 text-white hover:scale-105 active:scale-95"
                }
              `}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                  <span>Downloading...</span>
                </div>
              ) : (
                "Download Users CSV"
              )}
            </button>

            {message && (
              <div
                className={`
                p-4 rounded-lg text-sm
                ${
                  message.startsWith("Error")
                    ? "bg-red-900/50 text-red-300 border border-red-700"
                    : "bg-green-900/50 text-green-300 border border-green-700"
                }
              `}
              >
                {message}
              </div>
            )}

            <div className="text-gray-400 text-sm space-y-2">
              <p>
                <strong>Database:</strong> {SUPABASE_URL}
              </p>
              <p>
                <strong>Table:</strong> users
              </p>
              <p>
                <strong>Query:</strong> SELECT * FROM users
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
